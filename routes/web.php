<?php

use App\Http\Controllers\Dashboard\DonaturController;
use App\Http\Controllers\Dashboard\PanelController;
use App\Http\Controllers\Dashboard\ProgramController;
use App\Http\Controllers\Dashboard\DonasiController;
use App\Http\Controllers\Dashboard\RelawanController;
use App\Http\Controllers\Dashboard\UserController;
use App\Http\Controllers\Dashboard\VariablesController;
use App\Http\Controllers\Auth\GoogleController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', [GoogleController::class, 'login'])->name('login');
Route::get('/logout', [GoogleController::class, 'logout'])->name('logout');

Route::get('auth/google', [GoogleController::class, 'redirect']);
Route::get('auth/google/callback', [GoogleController::class, 'callback']);

Route::middleware(['auth'])->group(function () {
    Route::get('/panel', [PanelController::class, 'index']);

    Route::get('/donasi', function () {
        return redirect()->route('donasi.index');
    });

    Route::prefix('program')->group(function () {
        Route::get('/', [ProgramController::class, 'index'])->name('program.index');

        // Generate code route - must be before the {program} route to avoid conflicts
        Route::get('/generate-code', [ProgramController::class, 'generateUniqueCode'])->name('program.generate-code');

        Route::get('/{program}', [ProgramController::class, 'show'])->name('program.show');

        // Admin-only program management routes
        Route::middleware(['role:admin'])->group(function () {
            Route::post('/', [ProgramController::class, 'store'])->name('program.store');
            Route::put('/{program}', [ProgramController::class, 'update'])->name('program.update');
            Route::delete('/{program}', [ProgramController::class, 'destroy'])->name('program.destroy');
        });
    });

    // Profile routes
    Route::get('/profile', [ProfileController::class, 'show']);
    Route::match(['post', 'put'], '/profile', [ProfileController::class, 'update']);
});

// Admin routes
Route::middleware(['auth', 'role:admin'])->group(function () {
    // Group management routes under settings
    Route::prefix('settings')->group(function () {
        // User management routes
        Route::get('/user', [UserController::class, 'index']);
        Route::post('/user', [UserController::class, 'store']);
        Route::get('/user/{user}', [UserController::class, 'show']);
        Route::put('/user/{user}', [UserController::class, 'update']);

        // Variables route
        Route::get('/variables', [VariablesController::class, 'index']);
    });
});

// Donasi routes at root level
Route::middleware(['auth'])->group(function () {
    Route::get('/donasi', [DonasiController::class, 'index'])->name('donasi.index');
    Route::get('/donasi/ajax-donasi', [DonasiController::class, 'ajaxDonasi'])->name('donasi.ajax-donasi');
    Route::get('/dashboard/donasi/table', [\App\Http\Controllers\Dashboard\DonasiController::class, 'tablePartial'])->middleware('auth');
    Route::get('/donations/edit/{id}', [DonasiController::class, 'edit'])->name('donations.edit');
    Route::get('/donations/show/{id}', [DonasiController::class, 'show'])->name('donations.show');
    Route::post('/donations', [DonasiController::class, 'store'])->name('donations.store');
    Route::put('/donations/{id}', [DonasiController::class, 'update'])->name('donations.update');
    Route::delete('/donations/{id}', [DonasiController::class, 'destroy'])->name('donations.destroy');
});

// Donatur routes for both admin and staff
Route::middleware(['auth', 'role:admin,staff'])->group(function () {
    // Donatur routes
    Route::prefix('donatur')->group(function () {
        // User search routes for donatur management - place these BEFORE the resource routes
        Route::get('/get-user/{user}', [DonaturController::class, 'getUser']);
        Route::get('/get-donatur/{id}', [DonaturController::class, 'getDonatur']);

        // Regular donatur CRUD routes - view only for all
        Route::get('/', [DonaturController::class, 'index']);
        Route::get('search', [DonaturController::class, 'search']);
        Route::get('/{donatur}', [DonaturController::class, 'show']);
        Route::get('/{donatur}/edit', [DonaturController::class, 'edit']);

        Route::post('/', [DonaturController::class, 'store']);
        Route::put('/{donatur}', [DonaturController::class, 'update']);

        // Admin-only operations
        Route::middleware(['role:admin'])->group(function () {
            Route::delete('/{donatur}', [DonaturController::class, 'destroy']);
        });
    });
});

// Program routes
Route::middleware(['auth'])->group(function () {
    // Program search routes for component
    Route::get('/programs/search', [ProgramController::class, 'searchPrograms']);
    Route::get('/users/search', [DonaturController::class, 'searchUsers']);
    Route::get('/programs/get/{id}', [ProgramController::class, 'getProgram']);
    Route::get('/programs/get-multiple', [ProgramController::class, 'getMultiplePrograms']);
});

// Relawan routes
Route::middleware(['auth', 'role:admin,staff'])->group(function () {
    // Routes accessible to admin and staff users
    Route::get('/relawan', [RelawanController::class, 'index'])->name('relawan.index');

    // Admin-only routes
    Route::middleware(['role:admin'])->group(function () {
        Route::get('/relawan/create', [RelawanController::class, 'create'])->name('relawan.create');
        Route::post('/relawan', [RelawanController::class, 'store'])->name('relawan.store');
        Route::delete('/relawan/{relawan}', [RelawanController::class, 'destroy'])->name('relawan.destroy');
    });

    // These routes must come after the /relawan/create route
    Route::get('/relawan/{relawan}', [RelawanController::class, 'show'])->name('relawan.show');

    // Admin-only edit routes
    Route::middleware(['role:admin'])->group(function () {
        Route::get('/relawan/{relawan}/edit', [RelawanController::class, 'edit'])->name('relawan.edit');
        Route::put('/relawan/{relawan}', [RelawanController::class, 'update'])->name('relawan.update');
    });
});
